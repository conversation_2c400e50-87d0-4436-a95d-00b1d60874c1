# Linanok

Linanok is a professional URL shortening application designed specifically for organizations and companies. It provides a robust platform for managing and tracking shortened URLs at an enterprise level, helping businesses maintain their brand identity while sharing concise, memorable links.

## Features

### URL Management
- Centralized URL management dashboard
- Multiple domain support
- Link expiration and scheduling
- Password protection for sensitive links
- URL tagging and categorization

### Analytics & Tracking
- Detailed visit statistics and analytics
- Visitor location tracking
- Device and browser analytics
- Referrer tracking
- Track peak visit times

### Team & Access Control
- Team collaboration and access control
- Role-based access control (RBAC)
- Granular permissions management
- Department groups
- Domain ownership management
- User role management

## Support

For support, please contact our team at [<EMAIL>](mailto:<EMAIL>)

## Technical Documentation

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd linanok
```

2. Create a `.env` file in the root directory with the following variables:
```env
APP_KEY=your-app-key
DB_DATABASE=your-database-name
DB_USERNAME=your-database-user
DB_PASSWORD=your-database-password
```

3. Start the application:
```bash
docker-compose up -d
```

The application will be available at `http://localhost:8000`

### Configuration

- **Environment Variables**: Configure your application settings in the `.env` file
- **Database**: PostgreSQL is used for data storage
- **Caching**: Redis is used for caching
- **Queue Worker**: Background jobs are processed by a dedicated queue worker

## License

[Your License Here]

## Contributing

[Your Contributing Guidelines Here] 
